!function(){const t=document.createElement("link").relList;if(!(t&&t.supports&&t.supports("modulepreload"))){for(const t of document.querySelectorAll('link[rel="modulepreload"]'))e(t);new MutationObserver(t=>{for(const r of t)if("childList"===r.type)for(const t of r.addedNodes)"LINK"===t.tagName&&"modulepreload"===t.rel&&e(t)}).observe(document,{childList:!0,subtree:!0})}function e(t){if(t.ep)return;t.ep=!0;const e=function(t){const e={};return t.integrity&&(e.integrity=t.integrity),t.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),"use-credentials"===t.crossOrigin?e.credentials="include":"anonymous"===t.crossOrigin?e.credentials="omit":e.credentials="same-origin",e}(t);fetch(t.href,e)}}();const t={BASE_URL:"http://172.19.139.41:8087/mock/189/dwyzt",REALTIME_DATA:{url:"/getDataByRtKeyId",DATA_TYPES:{REAL_TIME_ACE:"130010:320000000000010034",CURRENT_COST:"130037:320000000000010010"}},SECTION_DATA:{url:"/getSectionTop5"},CPS_DATA:{url:"/getCPSList"}};function e(e,r){return`${t.BASE_URL}${t.CPS_DATA.url}?startDay=${e}&endDay=${r}`}const r={green:{color:"rgb(92, 239, 171)",colorWithWhite:"rgb(176, 255, 218)",colorTransparent:"rgba(92, 239, 171, 0.5)"},blue:{color:"rgb(68, 141, 245)",colorWithWhite:"rgb(156, 197, 255)",colorTransparent:"rgba(68, 141, 245, 0.5)"},yellow:{color:"rgb(236, 193, 121)",colorWithWhite:"rgb(250, 217, 161)",colorTransparent:"rgba(236, 193, 121, 0.5)"}},o="#1a3a66",a="#6c7e9a",n={left:"0%",right:"0%",bottom:"0%",containLabel:!0},i={trigger:"axis",backgroundColor:"rgba(10, 26, 51, 0.9)",borderColor:"#1a3a66",textStyle:{color:"#fff"}},s={textStyle:{color:"#c0c0c0"},top:"0%",right:"0%",itemHeight:8,itemStyle:{borderWidth:4}},l={type:"category",axisLine:{lineStyle:{color:o}},axisLabel:{color:a},splitLine:{show:!1}},c={type:"value",nameTextStyle:{color:"#d1d7db"},axisLine:{lineStyle:{color:o}},axisLabel:{color:a},splitLine:{lineStyle:{color:o,type:"dashed"}}},h={width:2};function d(t={}){return{...n,...t}}function u(t={}){return{...i,formatter:t=>{let e=`${t[0].axisValue}<br/>`;return t.forEach(t=>{e+=`${t.marker} ${t.seriesName}: ${t.value}<br/>`}),e},...t}}function m(t,e={}){return{...s,data:t,...e}}function f(t,e={}){return{...l,data:t,...e}}function p(t,e={},r={}){const a={...c,name:t,min:e.min||null,max:e.max||null,interval:e.interval||null,...r};return e.showMinorTick&&(a.minorSplitLine={show:!0,lineStyle:{color:o,opacity:.3}}),a}function y(t){return t.map(t=>function(t,e,o=!1,a=null){const{color:n}=r[t.colorType],i={name:t.name,type:"line",data:e,smooth:!0,symbol:"circle",symbolSize:t.symbolSize||6,showSymbol:t.showSymbol,itemStyle:{color:r[t.colorType].colorWithWhite,borderWidth:4,borderColor:r[t.colorType].colorTransparent},lineStyle:{...h,color:n},row:t,colorType:t.colorType};return o&&(i.lineStyle.type="dashed"),a&&(i.markPoint=a),i}(t,t.data,!1))}class b{constructor(t,r=null,o=null){this.container=document.getElementById(t),this.data=r,this.chart=null,this.dateRange=o||function(){const t=new Date,e=new Date;e.setDate(t.getDate()-6);const r=t=>t.toISOString().split("T")[0];return{startDay:r(e),endDay:r(t)}}(),this.apiUrl=e(this.dateRange.startDay,this.dateRange.endDay),this.init()}init(){this.container?(this.chart=echarts.init(this.container),this.data?this.render():this.fetchData(),window.addEventListener("resize",()=>{this.chart.resize()})):console.error("容器元素不存在")}async fetchData(){try{const t=await fetch(this.apiUrl),e=await t.json();"0000"===e.code&&e.data?(this.data=this.transformApiData(e.data),this.render()):console.error("CPS接口返回错误:",e)}catch(t){console.error("CPS接口调用失败:",t)}}transformApiData(t){const{CPS1List:e=[],CPS2List:r=[]}=t,o=[...e.map(t=>t.time),...r.map(t=>t.time)],a=[...new Set(o)].sort(),n=a,i=new Map,s=new Map;e.forEach(t=>{i.set(t.time,this.formatNumber(t.value))}),r.forEach(t=>{s.set(t.time,this.formatNumber(t.value))});return{xAxis:n,series:[{name:"当班CPS1",colorType:"green",showSymbol:!0,data:a.map(t=>i.get(t)||null)},{name:"当班CPS2",colorType:"blue",showSymbol:!0,data:a.map(t=>s.get(t)||null)}],yAxis:{min:0,max:300}}}formatNumber(t){if(null==t||""===t)return null;const e=parseFloat(t);return isNaN(e)?null:parseFloat(e.toFixed(1))}showError(t){this.chart&&this.chart.showLoading({text:t,color:"#c23531",textColor:"#c23531",maskColor:"rgba(255, 255, 255, 0.8)",zlevel:0})}render(){if(!this.data)return void this.showError("数据格式错误");this.chart&&this.chart.hideLoading();const{xAxis:t,series:e,yAxis:o}=this.data,a=function(t){const{xAxis:e,series:o,unit:a,yAxis:n,gridConfig:i={},tooltipConfig:s={},legendConfig:l={},xAxisConfig:c={},yAxisConfig:h={}}=t,y=o.map(t=>({name:t.name,itemStyle:{color:r[t.colorType].colorWithWhite,borderColor:r[t.colorType].colorTransparent}}));return{grid:d(i),tooltip:u(s),legend:m(y,l),xAxis:f(e,c),yAxis:p(a,n,h),series:o}}({xAxis:t,series:y(e),yAxis:o,gridConfig:{top:"20%"},legendConfig:{}});this.chart.setOption(a)}updateData(t){this.data=t,this.render()}updateDateRange(t,r){this.dateRange={startDay:t,endDay:r},this.apiUrl=e(t,r),this.fetchData()}refreshData(){this.fetchData()}destroy(){this.chart&&(this.chart.dispose(),this.chart=null),window.removeEventListener("resize",this.chart.resize)}}class g{constructor(e,r=null){this.container=document.getElementById(e),this.data=r,this.apiUrl=function(e=["REAL_TIME_ACE","CURRENT_COST"]){const r=e.map(e=>t.REALTIME_DATA.DATA_TYPES[e]).filter(Boolean).join(",");return`${t.BASE_URL}${t.REALTIME_DATA.url}?rtKeyStr=${r}`}(["REAL_TIME_ACE","CURRENT_COST"]),this.init()}init(){this.container?this.data?this.render():this.fetchData():console.error("容器元素不存在")}async fetchData(){try{const t=await fetch(this.apiUrl),e=await t.json();"0000"===e.code&&e.data&&Array.isArray(e.data)?(this.data=this.transformApiData(e.data),this.render()):console.error("接口返回错误:",e)}catch(t){console.error("接口调用失败:",t)}}transformApiData(t){return{realTimeACE:t[0],currentCost:t[1]}}formatNumber(t){if(null==t||""===t)return"N/A";const e=parseFloat(t);return isNaN(e)?t:e.toFixed(1)}showError(t){const e=this.container.querySelector(".info-box:nth-child(1) .info-box-value"),r=this.container.querySelector(".info-box:nth-child(2) .info-box-value");e&&r&&(e.textContent=t,r.textContent=t)}render(){if(!this.data)return void this.showError("数据格式错误");const t=this.container.querySelector(".info-box:nth-child(1) .info-box-value"),e=this.container.querySelector(".info-box:nth-child(2) .info-box-value");t&&e?(t.textContent=this.data.currentCost,e.textContent=this.data.realTimeACE):console.error("信息显示元素不存在")}updateData(t){this.data=t,this.render()}refreshData(){this.fetchData()}}class A{constructor(e,r=null){this.container=document.getElementById(e),this.data=r,this.apiUrl=`${t.BASE_URL}${t.SECTION_DATA.url}`,this.init()}init(){this.container?this.data?this.render():this.fetchData():console.error("容器元素不存在")}async fetchData(){try{const t=await fetch(this.apiUrl),e=await t.json();"0000"===e.code&&e.data?(this.data=this.transformApiData(e.data),this.render()):console.error("接口返回错误:",e)}catch(t){console.error("接口调用失败:",t)}}transformApiData(t){return{columns:["断面名称","电压(kV)","限值(MW)","实际值(MW)","发生时间","最大值(MW)","差值(MW)"],data:t.map(t=>[t.sectionName,this.formatNumber(t.volt,1),this.formatNumber(t.limitValue,1),this.formatNumber(t.actualValue,1),t.occurTime,this.formatNumber(t.maxValue,1),this.formatNumber(t.diffValue,1)])}}formatNumber(t,e=1){if(null==t||""===t)return"N/A";const r=parseFloat(t);return isNaN(r)?t:r.toFixed(e)}showError(t){this.container.innerHTML=`<div class="error-message">${t}</div>`}render(){if(!this.data||!this.data.columns||!this.data.data)return void this.showError("数据格式错误");const{columns:t,data:e}=this.data,r=document.createElement("table");r.className="section-table";const o=document.createElement("thead"),a=document.createElement("tr");t.forEach(t=>{const e=document.createElement("th"),r=document.createElement("span");r.textContent=t,e.appendChild(r),a.appendChild(e)}),o.appendChild(a),r.appendChild(o);const n=document.createElement("tbody");e.forEach(t=>{const e=document.createElement("tr");t.forEach(t=>{const r=document.createElement("td");r.textContent=t,e.appendChild(r)}),n.appendChild(e)}),r.appendChild(n),this.container.innerHTML="",this.container.appendChild(r)}updateData(t){this.data=t,this.render()}refreshData(){this.fetchData()}}document.addEventListener("DOMContentLoaded",()=>{const t=new A("section-table"),e=new b("cps-chart"),r=new g("info-container");window.powerCharts={sectionTable:t,cpsChart:e,infoDisplay:r},console.log("全网平衡监视系统初始化完成")});
