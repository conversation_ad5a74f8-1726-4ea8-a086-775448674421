# PowerCharts 项目

基于 Vite 构建的图表展示项目，支持环境变量配置和代理设置。

## 项目结构

```
PowerCharts/
├── assets/          # 静态资源文件
├── css/             # 样式文件
├── js/              # JavaScript 源码
│   ├── components/  # 组件文件
│   ├── config/      # 配置文件
│   ├── data.js      # 数据处理
│   └── main.js      # 主入口文件
├── index.html       # 主页面
├── vite.config.js   # Vite 配置文件
├── package.json     # 项目依赖配置
└── .env*            # 环境变量配置文件
```

## 环境变量配置

项目支持通过环境变量配置 API 基础地址：

### 环境变量文件

- `.env` - 默认环境变量
- `.env.development` - 开发环境变量
- `.env.production` - 生产环境变量
- `.env.example` - 环境变量示例文件

### 配置说明

```bash
# API基础地址
VITE_API_BASE_URL=/dwyztApp/dwyzt
```

**注意：** Vite 中的环境变量必须以 `VITE_` 前缀开头才能在客户端代码中访问。

## 代理配置

开发环境下，Vite 会自动将 `/dwyztApp/dwyzt` 路径代理到 `http://*************:8087/mock/189/dwyzt`。

代理配置位于 `vite.config.js` 文件中：

```javascript
proxy: {
  '/dwyztApp/dwyzt': {
    target: 'http://*************:8087/mock/189/dwyzt',
    changeOrigin: true,
    rewrite: (path) => path.replace(/^\/dwyztApp\/dwyzt/, '')
  }
}
```

## 开发指南

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

启动开发服务器，默认端口 3000，支持热重载。

### 构建生产版本

```bash
npm run build
```

构建优化后的生产版本到 `dist` 目录。

### 预览生产版本

```bash
npm run preview
```

本地预览构建后的生产版本。

## API 配置

API 配置位于 `js/config/ChartConfig.js` 文件中，支持环境变量动态配置：

```javascript
export const API_CONFIG = {
  // 基础API地址 - 从环境变量获取
  BASE_URL: import.meta.env.VITE_API_BASE_URL || "/dwyztApp/dwyzt",
  // ... 其他配置
};
```

## 部署说明

1. **开发环境**：使用代理路径 `/dwyztApp/dwyzt`
2. **生产环境**：直接使用完整的 API 地址 `http://*************:8087/mock/189/dwyzt`

根据部署环境修改对应的 `.env` 文件即可。
